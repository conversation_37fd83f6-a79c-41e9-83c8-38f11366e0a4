class MyClass {
    #internalState = 10;
    public static readonly MY_CONSTANT = 'some_value'; // Make constant readonly

    constructor(private name: string, public age: number) { // Fix naming convention
    }

    get someValue() { // Fix naming convention
        console.log("Accessing some value!");
        return this.#internalState;
    }

    doSomething = () => {
        if (this.#internalState > 5) {
            throw new Error(`Internal state ${this.#internalState} exceeds threshold`); // More descriptive error
        }

        for (const item of [1, 2, 3]) { // Use for...of instead of for...in
            console.log(item);
        }

        const value = Number('123'); // Safe conversion instead of type assertion
        console.log('test'); // Use string literal instead of new String()
    }

    private anotherMethod(param: unknown) { // Use unknown instead of any
        // Remove debugger statement
        const arr = Array.from({ length: 3 }, (_, i) => i); // Create proper array with values
        arr.forEach(item => console.log(item));
    }
}

function processData(data: Record<string, unknown> | null | undefined) { // More specific type
    if (data === null || data === undefined) { // Use strict equality
        console.log("Data is null or undefined");
    }
}

export default class MyUtil {
    static helper(): string { // Add return type annotation
        return 'This is a helper';
    }
}