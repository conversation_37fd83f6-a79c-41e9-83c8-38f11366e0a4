name: "Copilot Setup Steps"

# Tự động chạy các setup steps khi có thay đổi để dễ dàng validation, và
# cho phép manual testing thông qua tab "Actions" của repository
on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  # Job BẮT BUỘC phải có tên `copilot-setup-steps` nếu không Copilot sẽ không nhận diện được.
  copilot-setup-steps:
    runs-on: ubuntu-latest

    # Đặt permissions ở mức thấp nhất có thể cần thiết cho các steps của bạn.
    # Copilot sẽ được cấp token riêng cho các operations của nó.
    permissions:
      # Nếu bạn muốn clone repository như một phần của setup steps, ví dụ để install dependencies, bạn sẽ cần permission `contents: read`. <PERSON><PERSON><PERSON> bạn không clone repository trong setup steps, Copilot sẽ tự động làm điều này sau khi các steps hoàn thành.
      contents: read

    # Bạn có thể định nghĩa bất kỳ steps nào bạn muốn, và chúng sẽ chạy trước khi agent khởi động.
    # Nếu bạn không checkout code, Copilot sẽ làm điều này cho bạn.
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: "temurin"

      # - name: Set up Maven
      #   uses: actions/setup-java@v4
      #   with:
      #     java-version: "21"
      #     distribution: "temurin"
      #     cache: "maven"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          # cache: "pnpm"

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Current folder
        run: ls -la

      - name: Install JavaScript dependencies
        run: pnpm install --frozen-lockfile
        working-directory: ./frontend

      - name: Install Java dependencies
        run: mvn clean compile -DskipTests
        working-directory: ./backend
