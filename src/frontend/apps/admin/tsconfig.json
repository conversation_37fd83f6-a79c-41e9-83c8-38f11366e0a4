{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "@workspace/api-client/*": ["../../packages/api-client/src/*"], "@workspace/auth/*": ["../../packages/auth/src/*"], "@workspace/lib/*": ["../../packages/lib/src/*"], "@workspace/store/*": ["../../packages/store/src/*"], "@workspace/types/*": ["../../packages/types/src/*"], "@workspace/ui/*": ["../../packages/ui/src/*"]}}, "include": ["next-env.d.ts", "next.config.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}