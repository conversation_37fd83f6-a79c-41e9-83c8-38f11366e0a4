"use client";

import { createContext, useState, ReactNode } from "react";
import { UserModel } from "@workspace/auth/types/models";

export interface AuthContextType {
  user: UserModel | null;
  login: (user: UserModel) => void;
  logout: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<UserModel | null>(null);

  const login = (userData: UserModel) => {
    setUser(userData);
    // In a real app, you'd set a cookie or token here
  };

  const logout = () => {
    setUser(null);
    // TODO: Clear authentication cookies or tokens to fully log out the user
  };

  return (
    <AuthContext.Provider value={{ user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
