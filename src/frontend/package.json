{"name": "frontend-cms", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "typecheck": "turbo typecheck", "format": "prettier --write '**/*.{js,jsx,ts,tsx,md,mdx,json,yml,yaml,css,scss}'"}, "devDependencies": {"eslint": "^8.58.0", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "prettier": "^3.5.1", "turbo": "^2.4.2", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}}