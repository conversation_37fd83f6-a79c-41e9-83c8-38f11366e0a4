# Frontend CMS

## 1. <PERSON><PERSON><PERSON> trú<PERSON> thư mục ch<PERSON>h

```text
src/frontend/
├── apps/
│   ├── admin/              # Ứng dụng admin (Next.js App Router)
│   │   ├── app/            # Routing theo Next.js App Router, layout, page của admin
│   │   ├── hooks/          # Custom hooks (ví dụ: zustand store)
│   │   ├── lib/            # Helper, utils
│   │   ├── services/       # Service gọi API
│   │   ├── stores/         # Store zustand hoặc các state manager khác
│   │   ├── types/          # Định nghĩa type, model, interface
│   │   ├── ui-components/  # Các component UI dùng riêng cho admin (đặt tên là `ui-components` để tránh shadcn nhầm lẫn với file `components.json`)
│   │   └── ...             # Các file cấu hình, package, tsconfig...
│   └── web/                # Ứng dụng web (Next.js App Router)
│       ├── app/
│       ├── hooks/
│       ├── lib/
│       ├── services/
│       ├── stores/
│       ├── types/
│       ├── ui-components/
│       └── ...
├── packages/               # Các package dùng chung cho các apps (ui, auth, lib, store, types)
│   ├── api-client/         # Client gọi API, giao tiếp backend
│   ├── auth/               # Logic xác thực (authentication), đăng nhập, đăng ký, xác thực token
│   ├── eslint-config/      # Cấu hình ESLint dùng chung
│   ├── lib/                # Helper, utils, hàm tiện ích dùng chung
│   ├── store/              # State manager/store dùng chung (Zustand)
│   ├── types/              # Định nghĩa type, model, interface dùng chung
│   ├── typescript-config/  # Cấu hình TypeScript dùng chung
│   └── ui/                 # shadcn UI components dùng chung
├── pnpm-workspace.yaml
├── turbo.json
└── ...
```

---

## 2. Hướng dẫn cài đặt và chạy ứng dụng

### Yêu cầu

- Node.js >= 20
- pnpm (<https://pnpm.io/installation>)

### Cài đặt

```bash
cd src/frontend
pnpm install
```

### Chạy ứng dụng admin

```bash
cd apps/admin
pnpm dev
```

### Chạy ứng dụng web

```bash
cd apps/web
pnpm dev
```

---

## 3. Một số lưu ý

- Routing sử dụng Next.js App Router (tạo route bằng cách thêm file/folder trong `app/`).
- Các logic/phần dùng chung nên đặt ở `packages/`.
- Các logic/phần dùng riêng nên đặt trong từng app `admin` và `web`.
- Để mở rộng, chỉ cần thêm package hoặc module mới vào đúng thư mục tương ứng.
